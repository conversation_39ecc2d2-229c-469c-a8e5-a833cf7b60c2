<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Test Suite</title>
    
    <!-- CSS Dependencies -->
    <link rel="stylesheet" href="../../css/tokens.css">
    <link rel="stylesheet" href="snap-grid.css">
    
    <style>
        body {
            font-family: var(--font-family-sans, system-ui, -apple-system, sans-serif);
            margin: 0;
            padding: 20px;
            background: var(--color-surface-tertiary, #f8fafc);
            color: var(--color-text-primary, #111827);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: var(--color-surface-primary, #ffffff);
            border: 1px solid var(--color-border-primary, #e5e7eb);
            border-radius: var(--border-radius-lg, 12px);
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
        }
        
        .test-title {
            font-size: var(--font-size-xl, 20px);
            font-weight: var(--font-weight-semibold, 600);
            margin-bottom: 16px;
            color: var(--color-text-primary, #111827);
        }
        
        .test-description {
            color: var(--color-text-secondary, #6b7280);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .grid-container {
            height: 400px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            background: var(--color-primary-600, #2563eb);
            color: white;
            border: none;
            border-radius: var(--border-radius-md, 6px);
            cursor: pointer;
            font-size: var(--font-size-sm, 14px);
            transition: background-color 0.15s ease;
        }
        
        .btn:hover {
            background: var(--color-primary-700, #1d4ed8);
        }
        
        .btn-secondary {
            background: var(--color-surface-secondary, #f9fafb);
            color: var(--color-text-primary, #111827);
            border: 1px solid var(--color-border-primary, #e5e7eb);
        }
        
        .btn-secondary:hover {
            background: var(--color-surface-hover, #f3f4f6);
        }
        
        .info-panel {
            background: var(--color-surface-secondary, #f9fafb);
            border: 1px solid var(--color-border-primary, #e5e7eb);
            border-radius: var(--border-radius-md, 6px);
            padding: 16px;
            margin-top: 16px;
            font-size: var(--font-size-sm, 14px);
        }
        
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: var(--color-surface-primary, #ffffff);
            border-radius: var(--border-radius-sm, 4px);
        }
        
        .stat-value {
            font-weight: var(--font-weight-semibold, 600);
            color: var(--color-primary-600, #2563eb);
        }
        
        .stat-label {
            font-size: var(--font-size-xs, 12px);
            color: var(--color-text-secondary, #6b7280);
        }
        
        /* Dark mode toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 12px;
            }
            
            .test-section {
                padding: 16px;
            }
            
            .grid-container {
                height: 300px;
            }
            
            .controls {
                gap: 8px;
            }
            
            .btn {
                padding: 6px 12px;
                font-size: var(--font-size-xs, 12px);
            }
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button class="btn btn-secondary" onclick="toggleTheme()">🌙 Toggle Theme</button>
    </div>

    <div class="test-container">
        <h1>SnapGrid Test Suite</h1>
        <p>Comprehensive testing for the SnapGrid data grid component with AG Grid-inspired features.</p>

        <!-- Basic Grid Test -->
        <div class="test-section">
            <h2 class="test-title">1. Basic Data Grid</h2>
            <p class="test-description">
                Basic grid with sample product data, sorting, and column menus.
            </p>
            
            <div class="controls">
                <button class="btn" onclick="refreshBasicGrid()">Refresh Data</button>
                <button class="btn" onclick="selectAllBasic()">Select All</button>
                <button class="btn btn-secondary" onclick="exportBasicGrid()">Export All CSV</button>
                <button class="btn btn-secondary" onclick="exportSelectedBasic()">Export Selected CSV</button>
                <button class="btn btn-secondary" onclick="clearBasicSelection()">Clear Selection</button>
            </div>
            
            <div id="basic-grid" class="grid-container"></div>
            
            <div class="info-panel">
                <strong>Features:</strong> Basic rendering, column sorting, row selection, column menus
                <div id="basic-info" class="performance-stats"></div>
            </div>
        </div>

        <!-- Virtual Scrolling Test -->
        <div class="test-section">
            <h2 class="test-title">2. Virtual Scrolling Performance</h2>
            <p class="test-description">
                Large dataset (10,000+ rows) with virtual scrolling for performance testing.
            </p>
            
            <div class="controls">
                <button class="btn" onclick="loadLargeDataset(1000)">Load 1K Rows</button>
                <button class="btn" onclick="loadLargeDataset(10000)">Load 10K Rows</button>
                <button class="btn" onclick="loadLargeDataset(50000)">Load 50K Rows</button>
                <button class="btn btn-secondary" onclick="measureScrollPerformance()">Measure Performance</button>
            </div>
            
            <div id="virtual-grid" class="grid-container"></div>
            
            <div class="info-panel">
                <strong>Features:</strong> Virtual scrolling, performance monitoring, memory optimization
                <div id="virtual-info" class="performance-stats"></div>
            </div>
        </div>

        <!-- Advanced Filtering Test -->
        <div class="test-section">
            <h2 class="test-title">3. Advanced Filtering & Column Management</h2>
            <p class="test-description">
                Comprehensive filtering options, column pinning, autosize, and column management features.
            </p>

            <div class="controls">
                <button class="btn" onclick="applyFilters()">Apply Multiple Filters</button>
                <button class="btn" onclick="testBlankFilters()">Test Blank Filters</button>
                <button class="btn" onclick="testTextFilters()">Test Text Filters</button>
                <button class="btn" onclick="pinLeftColumns()">Pin Left Columns</button>
                <button class="btn" onclick="autosizeAllColumns()">Autosize All</button>
                <button class="btn btn-secondary" onclick="resetAdvancedGrid()">Reset</button>
            </div>

            <div id="advanced-grid" class="grid-container"></div>

            <div class="info-panel">
                <strong>Features:</strong> Advanced filtering (Contains, Blank, etc.), Column pinning, Autosize, Column chooser
                <div id="advanced-info" class="performance-stats"></div>
                <div style="margin-top: 12px; font-size: 12px; color: var(--color-text-secondary);">
                    <strong>Try:</strong> Right-click column headers for menu • Use filter dialogs • Pin columns left/right
                </div>
            </div>
        </div>

        <!-- Grouping & Editing Test -->
        <div class="test-section">
            <h2 class="test-title">4. Grouping & Data Editing</h2>
            <p class="test-description">
                Data grouping, cell editing, and advanced data manipulation features.
            </p>

            <div class="controls">
                <button class="btn" onclick="groupByMarketplace()">Group by Marketplace</button>
                <button class="btn" onclick="groupByStatus()">Group by Status</button>
                <button class="btn" onclick="enableEditing()">Enable Editing</button>
                <button class="btn btn-secondary" onclick="clearGrouping()">Clear Grouping</button>
            </div>

            <div id="grouping-grid" class="grid-container"></div>

            <div class="info-panel">
                <strong>Features:</strong> Data grouping, cell editing, validation, group headers
                <div id="grouping-info" class="performance-stats"></div>
            </div>
        </div>

        <!-- Custom Renderers Test -->
        <div class="test-section">
            <h2 class="test-title">5. Custom Cell Renderers</h2>
            <p class="test-description">
                Various cell types: currency, dates, booleans, links, and custom components.
            </p>
            
            <div class="controls">
                <button class="btn" onclick="toggleCurrencyFormat()">Toggle Currency Format</button>
                <button class="btn" onclick="toggleDateFormat()">Toggle Date Format</button>
                <button class="btn btn-secondary" onclick="addCustomRenderer()">Add Custom Renderer</button>
            </div>
            
            <div id="renderers-grid" class="grid-container"></div>
            
            <div class="info-panel">
                <strong>Features:</strong> Currency formatting, date formatting, boolean indicators, links, custom HTML
                <div id="renderers-info" class="performance-stats"></div>
            </div>
        </div>

        <!-- Responsive Design Test -->
        <div class="test-section">
            <h2 class="test-title">6. Responsive Design</h2>
            <p class="test-description">
                Grid behavior on different screen sizes and device orientations.
            </p>
            
            <div class="controls">
                <button class="btn" onclick="setTheme('default')">Default Theme</button>
                <button class="btn" onclick="setTheme('compact')">Compact Theme</button>
                <button class="btn" onclick="setTheme('comfortable')">Comfortable Theme</button>
                <button class="btn btn-secondary" onclick="simulateMobile()">Simulate Mobile</button>
                <button class="btn btn-secondary" onclick="testDestroy()">Test Destroy/Recreate</button>
            </div>
            
            <div id="responsive-grid" class="grid-container"></div>
            
            <div class="info-panel">
                <strong>Features:</strong> Responsive layout, theme variations, mobile optimization
                <div id="responsive-info" class="performance-stats"></div>
            </div>
        </div>

        <!-- Column Width Synchronization Test -->
        <div class="test-section">
            <h2 class="test-title">7. Column Width Synchronization</h2>
            <p class="test-description">
                Test column resizing to ensure header cells and data cells maintain synchronized widths.
            </p>

            <div class="controls">
                <button onclick="testColumnWidthSync()">Test Width Sync</button>
                <button onclick="resetSyncTest()">Reset Test</button>
                <button onclick="autosizeSyncColumns()">Autosize All</button>
            </div>

            <div id="sync-grid" class="grid-container"></div>

            <div class="info-panel">
                <strong>Features:</strong> Column resizing, width synchronization, autosize
                <div id="sync-info" class="performance-stats"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="dummy-grid-data.js"></script>
    <script src="snap-grid.js"></script>
    
    <script>
        // Global grid instances
        let basicGrid, virtualGrid, advancedGrid, groupingGrid, renderersGrid, responsiveGrid, syncGrid;

        // Initialize all test grids
        document.addEventListener('DOMContentLoaded', function() {
            initializeBasicGrid();
            initializeVirtualGrid();
            initializeAdvancedGrid();
            initializeGroupingGrid();
            initializeRenderersGrid();
            initializeResponsiveGrid();
            initializeSyncGrid();
        });
        
        // Theme management
        function toggleTheme() {
            document.body.classList.toggle('dark');
            // Update all grids with new theme
            [basicGrid, virtualGrid, advancedGrid, groupingGrid, renderersGrid, responsiveGrid].forEach(grid => {
                if (grid && grid.container) {
                    grid.container.classList.toggle('dark');
                }
            });
        }
        
        function setTheme(theme) {
            if (responsiveGrid) {
                responsiveGrid.container.className = `snap-grid ${theme}`;
                updateInfo('responsive-info', responsiveGrid);
            }
        }
        
        // Performance monitoring
        function updateInfo(elementId, grid) {
            const element = document.getElementById(elementId);
            if (!element || !grid) return;
            
            const stats = grid.performance || {};
            const dataLength = grid.state?.displayData?.length || 0;
            const selectedCount = grid.state?.selectedRowKeys?.size || 0;
            
            element.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${dataLength.toLocaleString()}</div>
                    <div class="stat-label">Total Rows</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${selectedCount}</div>
                    <div class="stat-label">Selected</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(stats.renderTime || 0).toFixed(1)}ms</div>
                    <div class="stat-label">Render Time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(stats.filterTime || 0).toFixed(1)}ms</div>
                    <div class="stat-label">Filter Time</div>
                </div>
            `;
        }
        
        // Basic Grid Functions
        function initializeBasicGrid() {
            const columns = [
                { field: 'asin', headerName: 'ASIN', width: 120, sortable: true },
                { field: 'title', headerName: 'Product Title', width: 300, sortable: true },
                { field: 'marketplace', headerName: 'Marketplace', width: 120, sortable: true },
                { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true },
                { field: 'revenue', headerName: 'Revenue', width: 120, type: 'currency', sortable: true },
                { field: 'status', headerName: 'Status', width: 100, sortable: true }
            ];

            basicGrid = new SnapGrid('basic-grid', {
                columns: columns,
                data: generateProductData(100),
                sortable: true,
                filterable: true,
                selectable: true,
                virtualScrolling: false,
                performance: true
            });

            basicGrid.on('rendered', () => updateInfo('basic-info', basicGrid));
            basicGrid.on('selectionChanged', () => updateInfo('basic-info', basicGrid));
        }

        function refreshBasicGrid() {
            if (basicGrid) {
                basicGrid.updateData(generateProductData(100));
                updateInfo('basic-info', basicGrid);
            }
        }

        function selectAllBasic() {
            if (basicGrid) {
                basicGrid.selectAll();
            }
        }

        function exportBasicGrid() {
            if (basicGrid) {
                basicGrid.exportCSV({
                    selectedOnly: false,
                    visibleOnly: true,
                    filename: 'basic-grid-export.csv'
                });
            }
        }

        function exportSelectedBasic() {
            if (basicGrid) {
                basicGrid.exportCSV({
                    selectedOnly: true,
                    visibleOnly: true,
                    filename: 'basic-grid-selected.csv'
                });
            }
        }

        function clearBasicSelection() {
            if (basicGrid) {
                basicGrid.clearSelection();
            }
        }

        // Virtual Scrolling Functions
        function initializeVirtualGrid() {
            const columns = [
                { field: 'id', headerName: 'ID', width: 80 },
                { field: 'asin', headerName: 'ASIN', width: 120 },
                { field: 'title', headerName: 'Product Title', width: 250 },
                { field: 'sales', headerName: 'Sales', width: 100, type: 'number' },
                { field: 'revenue', headerName: 'Revenue', width: 120, type: 'currency' },
                { field: 'marketplace', headerName: 'Marketplace', width: 120 }
            ];

            virtualGrid = new SnapGrid('virtual-grid', {
                columns: columns,
                data: generateProductData(1000),
                virtualScrolling: true,
                sortable: true,
                filterable: true,
                performance: true,
                rowHeight: 35
            });

            virtualGrid.on('rendered', () => updateInfo('virtual-info', virtualGrid));
            virtualGrid.on('scroll', () => updateInfo('virtual-info', virtualGrid));
        }

        function loadLargeDataset(size) {
            if (virtualGrid) {
                const startTime = performance.now();
                virtualGrid.updateData(generateProductData(size));
                const loadTime = performance.now() - startTime;
                updateInfo('virtual-info', virtualGrid);
                console.log(`Loaded ${size} rows in ${loadTime.toFixed(1)}ms`);
            }
        }

        function measureScrollPerformance() {
            if (virtualGrid) {
                const viewport = virtualGrid.elements.viewport;
                const startTime = performance.now();
                let scrollCount = 0;

                const scrollInterval = setInterval(() => {
                    viewport.scrollTop += 100;
                    scrollCount++;

                    if (scrollCount >= 50) {
                        clearInterval(scrollInterval);
                        const endTime = performance.now();
                        const avgTime = (endTime - startTime) / scrollCount;
                        alert(`Average scroll time: ${avgTime.toFixed(2)}ms per scroll`);
                    }
                }, 50);
            }
        }

        // Advanced Features Functions
        function initializeAdvancedGrid() {
            const columns = [
                { field: 'asin', headerName: 'ASIN', width: 120, sortable: true, filterable: true },
                { field: 'title', headerName: 'Product Title', width: 300, sortable: true, filterable: true },
                { field: 'marketplace', headerName: 'Marketplace', width: 120, sortable: true, filterable: true, groupable: true },
                { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true, filterable: true, editable: true },
                { field: 'revenue', headerName: 'Revenue', width: 120, type: 'currency', sortable: true, filterable: true },
                { field: 'status', headerName: 'Status', width: 100, sortable: true, filterable: true },
                { field: 'lastUpdated', headerName: 'Last Updated', width: 150, type: 'date', sortable: true }
            ];

            advancedGrid = new SnapGrid('advanced-grid', {
                columns: columns,
                data: generateProductData(500),
                sortable: true,
                filterable: true,
                groupable: true,
                selectable: true,
                editable: false,
                virtualScrolling: true,
                performance: true
            });

            advancedGrid.on('rendered', () => updateInfo('advanced-info', advancedGrid));
            advancedGrid.on('filterChanged', () => updateInfo('advanced-info', advancedGrid));
            advancedGrid.on('groupChanged', () => updateInfo('advanced-info', advancedGrid));
        }

        function applyFilters() {
            if (advancedGrid) {
                // Apply multiple filters to demonstrate functionality
                advancedGrid.setFilter('marketplace', {
                    type: 'text',
                    operator: 'contains',
                    value: 'Amazon'
                });

                advancedGrid.setFilter('sales', {
                    type: 'number',
                    operator: 'greaterThan',
                    value: 50
                });

                advancedGrid.setFilter('status', {
                    type: 'text',
                    operator: 'notEquals',
                    value: 'Inactive'
                });

                alert('Applied filters: Marketplace contains "Amazon", Sales > 50, Status not "Inactive"');
            }
        }

        function groupByMarketplace() {
            if (advancedGrid) {
                advancedGrid.setGroupConfig({ field: 'marketplace' });
            }
        }

        function enableEditing() {
            if (advancedGrid) {
                advancedGrid.config.editable = true;
                advancedGrid.render();
                alert('Editing enabled! Click on Sales cells to edit.');
            }
        }

        function resetAdvancedGrid() {
            if (advancedGrid) {
                advancedGrid.state.filterConfig = {};
                advancedGrid.state.groupConfig = null;
                advancedGrid.config.editable = false;
                advancedGrid.state.pinnedColumns = { left: [], right: [] };
                advancedGrid.processData();
                advancedGrid.render();
                updateInfo('advanced-info', advancedGrid);
            }
        }

        // New Advanced Filter Test Functions
        function testBlankFilters() {
            if (advancedGrid) {
                // Test blank and not blank filters
                advancedGrid.setFilter('status', {
                    type: 'text',
                    operator: 'notBlank',
                    value: ''
                });

                alert('Applied "Not Blank" filter to Status column');
            }
        }

        function testTextFilters() {
            if (advancedGrid) {
                // Test various text filters
                advancedGrid.setFilter('title', {
                    type: 'text',
                    operator: 'startsWith',
                    value: 'Premium'
                });

                advancedGrid.setFilter('marketplace', {
                    type: 'text',
                    operator: 'endsWith',
                    value: 'US'
                });

                alert('Applied text filters: Title starts with "Premium", Marketplace ends with "US"');
            }
        }

        function pinLeftColumns() {
            if (advancedGrid) {
                advancedGrid.pinColumn('asin', 'left');
                advancedGrid.pinColumn('title', 'left');
                alert('Pinned ASIN and Title columns to the left');
            }
        }

        function autosizeAllColumns() {
            if (advancedGrid) {
                advancedGrid.autosizeAllColumns();
                alert('Auto-sized all columns to fit content');
            }
        }

        // Grouping Grid Functions
        function initializeGroupingGrid() {
            const columns = [
                { field: 'asin', headerName: 'ASIN', width: 120, sortable: true },
                { field: 'title', headerName: 'Product Title', width: 300, sortable: true },
                { field: 'marketplace', headerName: 'Marketplace', width: 120, sortable: true, groupable: true },
                { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true, editable: true },
                { field: 'revenue', headerName: 'Revenue', width: 120, type: 'currency', sortable: true },
                { field: 'status', headerName: 'Status', width: 100, sortable: true, groupable: true }
            ];

            groupingGrid = new SnapGrid('grouping-grid', {
                columns: columns,
                data: generateProductData(300),
                sortable: true,
                filterable: true,
                groupable: true,
                selectable: true,
                editable: false,
                virtualScrolling: true,
                performance: true
            });

            groupingGrid.on('rendered', () => updateInfo('grouping-info', groupingGrid));
            groupingGrid.on('groupChanged', () => updateInfo('grouping-info', groupingGrid));
        }

        function groupByStatus() {
            if (groupingGrid) {
                groupingGrid.setGroupConfig({ field: 'status' });
            }
        }

        function clearGrouping() {
            if (groupingGrid) {
                groupingGrid.setGroupConfig(null);
            }
        }

        // Custom Renderers Functions
        function initializeRenderersGrid() {
            const columns = [
                { field: 'asin', headerName: 'ASIN', width: 120 },
                { field: 'title', headerName: 'Product Title', width: 200 },
                {
                    field: 'revenue',
                    headerName: 'Revenue',
                    width: 120,
                    type: 'currency',
                    currencyFormat: { currency: 'USD', locale: 'en-US' }
                },
                {
                    field: 'lastUpdated',
                    headerName: 'Last Updated',
                    width: 150,
                    type: 'date',
                    dateFormat: { locale: 'en-US', options: { dateStyle: 'medium' } }
                },
                {
                    field: 'active',
                    headerName: 'Active',
                    width: 80,
                    type: 'boolean'
                },
                {
                    field: 'asin',
                    headerName: 'Amazon Link',
                    width: 120,
                    type: 'link',
                    linkFormat: {
                        href: (value) => `https://amazon.com/dp/${value}`,
                        text: () => 'View on Amazon',
                        target: '_blank'
                    }
                },
                {
                    field: 'status',
                    headerName: 'Status Badge',
                    width: 120,
                    cellRenderer: (params) => {
                        const status = params.value;
                        const colors = {
                            'Active': 'green',
                            'Inactive': 'red',
                            'Pending': 'orange'
                        };
                        const color = colors[status] || 'gray';
                        return `<span style="background: ${color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">${status}</span>`;
                    }
                }
            ];

            renderersGrid = new SnapGrid('renderers-grid', {
                columns: columns,
                data: generateProductData(200),
                sortable: true,
                virtualScrolling: true,
                performance: true
            });

            renderersGrid.on('rendered', () => updateInfo('renderers-info', renderersGrid));
        }

        function toggleCurrencyFormat() {
            if (renderersGrid) {
                const revenueColumn = renderersGrid.config.columns.find(col => col.field === 'revenue');
                if (revenueColumn) {
                    const currentCurrency = revenueColumn.currencyFormat?.currency || 'USD';
                    revenueColumn.currencyFormat.currency = currentCurrency === 'USD' ? 'EUR' : 'USD';
                    renderersGrid.render();
                }
            }
        }

        function toggleDateFormat() {
            if (renderersGrid) {
                const dateColumn = renderersGrid.config.columns.find(col => col.field === 'lastUpdated');
                if (dateColumn) {
                    const currentStyle = dateColumn.dateFormat?.options?.dateStyle || 'medium';
                    dateColumn.dateFormat.options.dateStyle = currentStyle === 'medium' ? 'full' : 'medium';
                    renderersGrid.render();
                }
            }
        }

        function addCustomRenderer() {
            if (renderersGrid) {
                // Add a new column with a complex custom renderer
                const newColumn = {
                    field: 'sales',
                    headerName: 'Sales Chart',
                    width: 150,
                    cellRenderer: (params) => {
                        const value = params.value || 0;
                        const maxValue = 200;
                        const percentage = Math.min((value / maxValue) * 100, 100);
                        return `
                            <div style="display: flex; align-items: center; width: 100%;">
                                <div style="flex: 1; background: #e5e7eb; height: 8px; border-radius: 4px; margin-right: 8px;">
                                    <div style="background: #3b82f6; height: 100%; width: ${percentage}%; border-radius: 4px;"></div>
                                </div>
                                <span style="font-size: 12px; color: #6b7280;">${value}</span>
                            </div>
                        `;
                    }
                };

                renderersGrid.config.columns.push(newColumn);
                renderersGrid.render();
                alert('Added custom sales chart renderer!');
            }
        }

        // Responsive Design Functions
        function initializeResponsiveGrid() {
            const columns = [
                { field: 'asin', headerName: 'ASIN', width: 120 },
                { field: 'title', headerName: 'Product Title', width: 250 },
                { field: 'marketplace', headerName: 'Marketplace', width: 120 },
                { field: 'sales', headerName: 'Sales', width: 100, type: 'number' },
                { field: 'revenue', headerName: 'Revenue', width: 120, type: 'currency' },
                { field: 'status', headerName: 'Status', width: 100 }
            ];

            responsiveGrid = new SnapGrid('responsive-grid', {
                columns: columns,
                data: generateProductData(300),
                sortable: true,
                filterable: true,
                selectable: true,
                virtualScrolling: true,
                performance: true,
                theme: 'default'
            });

            responsiveGrid.on('rendered', () => updateInfo('responsive-info', responsiveGrid));
        }

        function simulateMobile() {
            const container = document.getElementById('responsive-grid');
            if (container) {
                // Simulate mobile viewport
                container.style.width = '375px';
                container.style.margin = '0 auto';

                // Trigger resize
                if (responsiveGrid) {
                    responsiveGrid.resize();
                }

                setTimeout(() => {
                    container.style.width = '';
                    container.style.margin = '';
                    if (responsiveGrid) {
                        responsiveGrid.resize();
                    }
                }, 3000);

                alert('Simulating mobile view for 3 seconds...');
            }
        }

        function testDestroy() {
            if (responsiveGrid) {
                // Destroy the grid
                responsiveGrid.destroy();

                // Wait a moment then recreate
                setTimeout(() => {
                    initializeResponsiveGrid();
                    alert('Grid destroyed and recreated successfully!');
                }, 500);
            }
        }

        // Initialize Column Width Synchronization Grid
        function initializeSyncGrid() {
            const container = document.getElementById('sync-grid');
            if (!container) return;

            // Create test data with varied content lengths
            const syncData = [
                { id: 1, shortCol: 'A', mediumCol: 'Medium Text', longCol: 'This is a very long text that should test column width synchronization' },
                { id: 2, shortCol: 'BB', mediumCol: 'Another Medium', longCol: 'Another long text to verify that header and data cells maintain the same width' },
                { id: 3, shortCol: 'CCC', mediumCol: 'Third Medium', longCol: 'Yet another long text for comprehensive testing of the width sync feature' },
                { id: 4, shortCol: 'D', mediumCol: 'Fourth Text', longCol: 'Final long text to ensure the synchronization works across all rows' }
            ];

            const syncColumns = [
                { field: 'id', headerName: 'ID', width: 80, type: 'number' },
                { field: 'shortCol', headerName: 'Short Column', width: 120 },
                { field: 'mediumCol', headerName: 'Medium Column', width: 180 },
                { field: 'longCol', headerName: 'Long Column', width: 300 }
            ];

            syncGrid = new SnapGrid(container, {
                columns: syncColumns,
                data: syncData,
                height: '300px',
                resizable: true,
                sortable: true,
                theme: 'default'
            });

            updateSyncInfo();
        }

        // Test column width synchronization
        function testColumnWidthSync() {
            if (!syncGrid) return;

            const info = document.getElementById('sync-info');
            info.innerHTML = '<strong>Testing column width synchronization...</strong>';

            // Test different widths for each column
            const testWidths = [
                { field: 'id', width: 100 },
                { field: 'shortCol', width: 200 },
                { field: 'mediumCol', width: 250 },
                { field: 'longCol', width: 400 }
            ];

            let testIndex = 0;
            const runTest = () => {
                if (testIndex < testWidths.length) {
                    const { field, width } = testWidths[testIndex];
                    syncGrid.setColumnWidth(field, width);

                    // Verify synchronization
                    setTimeout(() => {
                        const headerCell = document.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
                        const dataCells = document.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);

                        let allSynced = true;
                        const headerWidth = headerCell ? headerCell.style.width : 'N/A';

                        dataCells.forEach(cell => {
                            if (cell.style.width !== headerWidth) {
                                allSynced = false;
                            }
                        });

                        console.log(`Column ${field}: Header width = ${headerWidth}, All cells synced = ${allSynced}`);
                        testIndex++;
                        runTest();
                    }, 100);
                } else {
                    info.innerHTML = '<strong>Width synchronization test completed!</strong> Check console for details.';
                }
            };

            runTest();
        }

        // Reset sync test
        function resetSyncTest() {
            if (!syncGrid) return;

            syncGrid.resetColumns();
            updateSyncInfo();
        }

        // Autosize sync columns
        function autosizeSyncColumns() {
            if (!syncGrid) return;

            syncGrid.autosizeAllColumns();
            updateSyncInfo();
        }

        // Update sync info
        function updateSyncInfo() {
            const info = document.getElementById('sync-info');
            if (info && syncGrid) {
                info.innerHTML = `
                    <strong>Grid Status:</strong> Ready for width synchronization testing<br>
                    <strong>Columns:</strong> ${syncGrid.config.columns.length}<br>
                    <strong>Rows:</strong> ${syncGrid.state.data.length}
                `;
            }
        }

        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Test error:', e.error);
            alert('Test error: ' + e.error.message);
        });
    </script>
</body>
</html>
